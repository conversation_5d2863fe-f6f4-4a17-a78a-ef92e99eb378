<script setup lang="ts">
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";
import { useRouter } from "vue-router";
import AddDashboardCard from "@/modules/monitor/components/AddDashboardCard.vue";
import DashboardCard from "@/modules/monitor/components/DashboardCard.vue";
import DashboardCardActions from "@/modules/monitor/components/DashboardCardActions.vue";
import { useRefreshService } from "@/modules/monitor/services/refreshService";

import type { Dashboard } from "~/modules/monitor/types";

const router = useRouter();

const refreshService = useRefreshService();
provide("refreshService", refreshService);

definePageMeta({
  layout: "monitor",
});

const { data: dashboards, refresh: refreshDashboards } = await useAsyncData<
  Dashboard[]
>("dashboards", () => {
  const headers = useRequestHeaders(["cookie"]);
  return $fetch("/api/dashboard", {
    headers,
  });
});

const createDashboard = async () => {
  refreshService.triggerCancel("monitor-dashboard-list");
  const dashboard = await $fetch("/api/dashboard", {
    method: "POST",
  });
  router.push(`/monitor/board/edit/${dashboard.id}`);
};

const handleCardClick = (dashboardId: number) => {
  refreshService.triggerCancel("monitor-dashboard-list");
  router.push(`/monitor/board/${dashboardId}`);
};

const handleEditClick = (dashboardId: number) => {
  refreshService.triggerCancel("monitor-dashboard-list");
  router.push(`/monitor/board/edit/${dashboardId}`);
};

const handleDeleteClick = (dashboardId: number) => {
  refreshService.triggerCancel("monitor-dashboard-list");
  deleteDashboard(dashboardId);
};

const deleteDashboard = async (id: number) => {
  await $fetch(`/api/dashboard/${id}`, {
    method: "DELETE",
  });
  await refreshDashboards();
};

let intervalTimer: NodeJS.Timeout | null = null;

onMounted(() => {
  intervalTimer = setInterval(() => {
    refreshService.triggerIntervalRefresh("monitor-dashboard-list");
  }, 1000 * 15);
});

onUnmounted(() => {
  if (intervalTimer) {
    clearInterval(intervalTimer);
    intervalTimer = null;
  }
});
</script>

<template>
  <ClientOnly>
    <Teleport to="#page-header-left">
      <Breadcrumb class="px-3">
        <BreadcrumbList>
          <BreadcrumbItem>
            <NuxtLink to="/monitor" as-child>
              <BreadcrumbLink> Monitor </BreadcrumbLink>
            </NuxtLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </Teleport>
  </ClientOnly>

  <div class="p-6" style="--color-theme-color: #3a7ca5">
    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 max-w-[1360px] mx-auto"
    >
      <AddDashboardCard :create-dashboard="createDashboard" />

      <ClientOnly>
        <DashboardCard
          v-for="dashboard in dashboards"
          :key="dashboard.id"
          :dashboard="dashboard"
          @click="() => handleCardClick(dashboard.id)"
        >
          <template #actions>
            <DashboardCardActions
              :on-edit="() => handleEditClick(dashboard.id)"
              :on-delete="() => handleDeleteClick(dashboard.id)"
            />
          </template>
        </DashboardCard>
      </ClientOnly>
    </div>
  </div>
</template>
