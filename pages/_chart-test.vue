<script setup lang="ts">
import type { Query } from "@cubejs-client/core";
import type { Component } from "vue";
import FourGridChart from "~/modules/monitor/components/Charts/FourGridChart.vue";
import Bar<PERSON><PERSON> from "~/modules/monitor/components/Charts/BarChart.vue";
import StackedBarChart from "~/modules/monitor/components/Charts/StackedBarChart.vue";
import SingleValueChart from "~/modules/monitor/components/Charts/SingleValueChart.vue";
import {
  UsersIcon,
  BarChart3Icon,
  TrendingUpIcon,
  PieChartIcon,
  AreaChartIcon,
} from "lucide-vue-next";

// Chart types for tabs
type ChartType = "single" | "fourgrid" | "bar" | "stacked";

interface ChartTab {
  id: ChartType;
  label: string;
  icon: Component;
}

const chartTabs: ChartTab[] = [
  { id: "single", label: "Single Value", icon: PieChartIcon },
  { id: "fourgrid", label: "Four Grid", icon: AreaChartIcon },
  { id: "bar", label: "Bar Chart", icon: BarChart3Icon },
  { id: "stacked", label: "Stacked Bar", icon: TrendingUpIcon },
];

// Use route query for active chart type
const route = useRoute();
const router = useRouter();

const activeChart = computed({
  get: () => (route.query.chart as ChartType) || "single",
  set: (value: ChartType) => {
    router.push({ query: { ...route.query, chart: value } });
  },
});

const query = ref<Query | null>(null);
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          Chart Components Test
        </h1>
        <p class="text-gray-600">
          Test different chart components with Cube.js query data. Try the
          time-based query example for the bar chart.
        </p>
      </div>

      <!-- Query Input -->
      <div class="mb-8">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Query JSON (Cube.js format)
        </label>
        <Textarea
          placeholder='{"timezone": "Asia/Shanghai", "timeDimensions": [{"dimension": "fct_flows_1m.timestamp", "granularity": "hour"}], "measures": ["fct_flows_1m.connect_cnt"]}'
          class="min-h-[100px] font-mono text-sm"
          @update:model-value="query = JSON.parse($event as string)"
        />
      </div>

      <!-- Chart Type Tabs -->
      <div class="mb-8">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in chartTabs"
              :key="tab.id"
              :class="[
                'flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                activeChart === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              ]"
              @click="activeChart = tab.id"
            >
              <component :is="tab.icon" class="w-4 h-4 mr-2" />
              {{ tab.label }}
            </button>
          </nav>
        </div>
      </div>

      <!-- Chart Display -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <!-- Single Value Chart -->
        <div v-if="activeChart === 'single'" class="flex justify-center">
          <div class="w-80 h-60">
            <SingleValueChart
              v-if="query"
              chart-id="single-value"
              title="Total Users"
              :icon="UsersIcon"
              :query="query"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              <div class="text-center">
                <PieChartIcon class="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Enter a query to display the single value chart</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Four Grid Chart -->
        <div v-if="activeChart === 'fourgrid'" class="flex justify-center">
          <div class="w-[500px]">
            <FourGridChart
              v-if="query"
              chart-id="four-grid"
              title="Dashboard Overview"
              :icon="BarChart3Icon"
              :icon-props="{ class: 'text-blue-500' }"
              :query="query"
              :tips="'Performance is good'"
            />
            <div
              v-else
              class="flex items-center justify-center h-60 text-gray-500"
            >
              <div class="text-center">
                <AreaChartIcon class="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Enter a query to display the four grid chart</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Bar Chart -->
        <div v-if="activeChart === 'bar'" class="flex justify-center">
          <div class="w-[600px]">
            <BarChart
              v-if="query"
              chart-id="bar"
              title="Hourly Connect Count"
              :icon="BarChart3Icon"
              :icon-props="{ class: 'text-purple-500' }"
              :query="query"
              :tips="'Connections trending over time'"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              <div class="text-center">
                <BarChart3Icon class="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Enter a query to display the bar chart</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Stacked Bar Chart -->
        <div v-if="activeChart === 'stacked'" class="flex justify-center">
          <div class="w-[600px]">
            <StackedBarChart
              v-if="query"
              chart-id="stacked"
              title="Connection Count by Link"
              :icon="TrendingUpIcon"
              :icon-props="{ class: 'text-green-500' }"
              :query="query"
              :tips="'Shows connection count grouped by link over time'"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              <div class="text-center">
                <TrendingUpIcon class="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Enter a query to display the stacked bar chart</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
