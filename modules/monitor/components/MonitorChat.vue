<script setup lang="ts">
import { useAutoScroll } from "~/hooks/useAutoScroll";
import { useMonitorEditStore } from "../stores/monitorEditStore";
import MessageInput from "./MessageInput.vue";
import Messages from "./Messages.vue";

const monitorStore = useMonitorEditStore();
const { messages, loadingDashboard, isRunning } = storeToRefs(monitorStore);
const hasMessages = computed(() => messages.value.length > 0);
const messagesContainerRef = ref();

const { triggerScrollToBottom } = useAutoScroll(
  messagesContainerRef,
  isRunning
);
const sendMessageToAgent = (message: string) => {
  monitorStore.sendMessageToAgent(message);
  nextTick(() => {
    triggerScrollToBottom("instant");
  });
};
</script>

<template>
  <div v-if="!loadingDashboard" class="min-w-96 h-full flex flex-col">
    <div v-if="hasMessages" class="flex-1 w-full h-full overflow-y-auto my-2">
      <Messages />
    </div>
    <div class="px-2 pb-2" :class="{ 'mt-56': !hasMessages }">
      <div
        v-if="!hasMessages"
        class="text-center text-stone-700 font-semibold text-3xl mb-6"
      >
        Build your own monitor
      </div>
      <MessageInput
        placeholder="Ask to build..."
        @submit="sendMessageToAgent"
      />
    </div>
  </div>
</template>
