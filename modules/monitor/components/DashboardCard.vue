<script setup lang="ts">
import MiniCard from "./MiniCard.vue";
import type { Dashboard } from "../types";

const props = defineProps<{
  dashboard: Dashboard;
}>();

const emit = defineEmits<{
  (e: "click"): void;
}>();

const miniCardConfig = computed(() => {
  if (!props.dashboard.miniCardConfig) {
    return null;
  }
  return {
    title: props.dashboard.name,
    singleValue: props.dashboard.miniCardConfig.singleValue,
    timeSeries: props.dashboard.miniCardConfig.timeSeries,
  };
});
</script>

<template>
  <MiniCard
    :title="dashboard.name"
    :config="miniCardConfig"
    @click="emit('click')"
  >
    <template #actions>
      <slot name="actions" />
    </template>
  </MiniCard>
</template>
