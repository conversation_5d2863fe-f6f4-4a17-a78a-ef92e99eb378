<script setup lang="ts">
import type { ChartType, Panel } from "../types";
import StackedBarChart from "./Charts/StackedBarChart.vue";
import MultiAxisLineChart from "./Charts/MultiAxisLineChart.vue";
import StackedLineChart from "./Charts/StackedLineChart.vue";
import BarChart from "./Charts/BarChart.vue";
import LineChart from "./Charts/LineChart.vue";
import SingleValueChart from "./Charts/SingleValueChart.vue";
import FourGridChart from "./Charts/FourGridChart.vue";

const props = defineProps<{
  config: Panel;
  debug?: boolean;
  colorIndex?: number;
}>();

const getChart = (chartType: ChartType) => {
  if (chartType === "stacked-bar") {
    return StackedBarChart;
  } else if (chartType === "bar") {
    return BarChart;
  } else if (chartType === "single-value") {
    return SingleValueChart;
  } else if (chartType === "four-grid") {
    return FourGridChart;
  } else if (chartType === "stacked-line") {
    return StackedLineChart;
  } else if (chartType === "multi-axis-line") {
    return MultiAxisLineChart;
  } else if (chartType === "line") {
    return LineChart;
  }
  return null;
};
</script>

<template>
  <ClientOnly>
    <div class="w-full h-full">
      <component
        :is="getChart(props.config.chart.type)"
        v-if="!debug"
        :chart-id="props.config.id"
        :title="props.config.title"
        :query="props.config.chart.query"
        :tips="props.config.tips"
        :color-index="props.colorIndex"
      />

      <div v-else class="w-full h-full border overflow-y-auto p-3">
        <pre class="text-xs">{{ JSON.stringify(props.config, null, 2) }}</pre>
      </div>
    </div>
  </ClientOnly>
</template>
