<script setup lang="ts">
import LineChartCard from "./LineChartCard.vue";
import BarChartCard from "./BarChartCard.vue";
import TableCard from "./TableCard.vue";
import { ref, onMounted, onUnmounted, watch } from "vue";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import type {
  CancellableRequest,
  ChartType,
  ChartDataType,
  BarLineData,
  TableDataItem,
} from "../types";

import {
  getClueDashboardDataWithRefresh,
  cancelClueRequest,
} from "~/modules/monitor/helpers/clueDataFetcher";
import { getChartTypeAndData } from "~/modules/monitor/helpers/chartTypeHelper";
import type { Query as DashboardQuery } from "@cubejs-client/core";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Props
interface Props {
  cubeQuery?: DashboardQuery;
  title?: string;
  chartId?: string;
  chartHeight?: string;
  tableMaxHeight?: string;
  refreshTrigger?: number;
  onRefreshComplete?: () => void;
  onRefreshError?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  cubeQuery: undefined,
  title: "",
  chartId: "default-chart-id",
  chartHeight: "",
  tableMaxHeight: "",
  refreshTrigger: 0,
  onRefreshComplete: undefined,
  onRefreshError: undefined,
});

const id = ref(props.chartId);

// 监听 chartId 变化，更新 id
watch(
  () => props.chartId,
  (newChartId) => {
    if (newChartId) {
      id.value = newChartId;
    }
  }
);

let currentRequest: CancellableRequest | null = null;

const chartData = ref<ChartDataType>();
const loading = ref(true);
const showChartType = ref<ChartType>();
const chartDataUnit = ref<string | undefined>();
const measuresData = ref<string[]>([]);
const measureUnits = ref<Record<string, string>>({});
const isInitialized = ref(false);

const fetchData = async (isManualRefresh = false) => {
  // 只在首次加载时显示loading，手动刷新时不显示
  if (!isInitialized.value && !isManualRefresh) {
    loading.value = true;
  }

  // 取消之前的请求和订阅
  if (currentRequest) {
    currentRequest.cancel();
    currentRequest = null;
  }

  cancelClueRequest(id.value);

  if (!props.cubeQuery) {
    console.warn("No cubeQuery provided to CubeChart");
    loading.value = false;
    if (isManualRefresh && props.onRefreshError) {
      props.onRefreshError();
    }
    return;
  }

  const _query: DashboardQuery = props.cubeQuery as DashboardQuery;

  currentRequest = getClueDashboardDataWithRefresh(id.value, _query, (data) => {
    handleChartResult(getChartTypeAndData(data));
  });

  try {
    const response = await currentRequest.promise;
    handleChartResult(getChartTypeAndData(response));
    isInitialized.value = true;

    if (isManualRefresh && props.onRefreshComplete) {
      props.onRefreshComplete();
    }
  } catch (error) {
    console.error("Error fetching chart data:", error);
    if (isManualRefresh && props.onRefreshError) {
      props.onRefreshError();
    }
  } finally {
    if (!isManualRefresh) {
      loading.value = false;
    }
    currentRequest = null;
  }
};
// 处理 chartResult
const handleChartResult = (
  chartResult: ReturnType<typeof getChartTypeAndData>
) => {
  chartData.value = chartResult.data;
  chartDataUnit.value = chartResult.unit;
  showChartType.value = chartResult.type;
  if (showChartType.value === "table") {
    measuresData.value = chartResult.measuresData as string[];
    measureUnits.value = chartResult.measureUnits || {};
  }
};

watch(
  [() => props.cubeQuery, id, () => props.refreshTrigger],
  async (newValues, oldValues) => {
    if (newValues.some((val, index) => val !== oldValues?.[index])) {
      if (props.cubeQuery) {
        // 检查是否是 refreshTrigger 的变化
        const isRefreshTriggerChange = newValues[2] !== oldValues?.[2];
        await fetchData(isRefreshTriggerChange);
      }
    }
  },
  { deep: true }
);

onMounted(async () => {
  await fetchData();
  const { dotSpinner } = await import("ldrs");
  dotSpinner.register();
});

onUnmounted(() => {
  // 取消本地引用的请求和订阅
  if (currentRequest) {
    currentRequest.cancel();
    currentRequest = null;
  }

  cancelClueRequest(id.value);
});
</script>
<template>
  <div class="w-full h-full">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center h-full">
      <l-dot-spinner size="40" speed="0.9" color="black" />
    </div>

    <!-- 无数据状态 -->
    <div
      v-else-if="!chartData?.length"
      class="flex justify-center items-center h-full"
    >
      <div class="text-gray-500">No chart data available</div>
    </div>

    <!-- 图表内容 -->
    <div
      v-show="isInitialized && chartData?.length"
      class="space-y-6 w-full h-full"
    >
      <LineChartCard
        v-show="showChartType === 'line'"
        :data="chartData as BarLineData[]"
        :unit="chartDataUnit as string"
        :chart-height="props.chartHeight"
        :title="props.title"
      />
      <BarChartCard
        v-show="showChartType === 'bar'"
        :data="chartData as BarLineData[]"
        :unit="chartDataUnit as string"
        :chart-height="props.chartHeight"
        :title="props.title"
      />
      <TableCard
        v-show="showChartType === 'table'"
        :data="chartData as TableDataItem[]"
        :measures-data="measuresData"
        :measure-units="measureUnits"
        :chart-height="props.chartHeight"
        :table-max-height="props.tableMaxHeight"
        :title="props.title"
      />
    </div>
  </div>
</template>
<style scoped></style>
