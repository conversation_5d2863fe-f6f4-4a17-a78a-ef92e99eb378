<script setup lang="ts">
import type {
  AssistantMessage as AssistantMessageType,
  UserMessage as UserMessageType,
} from "~/types/message";
import { useMonitorEditStore } from "../stores/monitorEditStore";
import UserMessage from "./UserMessage.vue";
import AssistantMessage from "./AssistantMessage.vue";

const monitorStore = useMonitorEditStore();
const { messages, isProcessing, isPending, isRunning } =
  storeToRefs(monitorStore);

const assistantMinHeight = computed(() => {
  return isRunning.value ? "100px" : undefined;
});

onMounted(async () => {
  const { dotPulse } = await import("ldrs");
  dotPulse.register();
});
</script>

<template>
  <div class="p-2 px-5">
    <div v-for="(message, index) in messages" :key="index">
      <div v-if="message.role === 'user'" class="flex justify-end mb-6">
        <UserMessage :message="message as UserMessageType" />
      </div>
      <div v-else-if="message.role === 'assistant'" class="mb-6">
        <AssistantMessage :message="message as AssistantMessageType" />
      </div>
    </div>
    <div
      :style="{
        minHeight: assistantMinHeight,
      }"
    >
      <ClientOnly>
        <l-dot-pulse
          v-if="isProcessing || isPending"
          size="38"
          speed="1.3"
          color="black"
          class="my-2"
        />
      </ClientOnly>
    </div>
  </div>
</template>
