<script setup lang="ts">
import { computed } from "vue";
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import type { CommonChartProps } from "./types";
import { useQueryData } from "../../hooks/chart/useQueryData";
import { useLineChartOption } from "../../hooks/chart/useLineChartOption";
import ChartWrapper from "./ChartWrapper.vue";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const props = defineProps<CommonChartProps>();
const query = computed(() => props.query);
const { resultSet, loading } = useQueryData(query);
const option = useLineChartOption(resultSet);
</script>

<template>
  <ChartWrapper
    :icon="icon"
    :icon-props="iconProps"
    :title="title"
    :loading="loading"
    :tips="tips"
    :color-index="colorIndex"
  >
    <div class="flex-1 min-h-0 w-full">
      <Line
        :data="option.chartData"
        :options="option.chartOption"
        :update-mode="'none'"
      />
    </div>
  </ChartWrapper>
</template>
