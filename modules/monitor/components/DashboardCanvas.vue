<script setup lang="ts">
import { useMonitorEditStore } from "../stores/monitorEditStore";
import { GridLayout } from "grid-layout-plus";
import DashboardPanel from "./DashboardPanel.vue";
import PlaceholderPanel from "./PlaceholderPanel.vue";
import { BugIcon } from "lucide-vue-next";
import TimeSelect from "./TimeSelect.vue";
import Refresh<PERSON>hart from "./RefreshChart.vue";
import { shortcuts } from "../helpers/timeSelect";
import { usePanels } from "../hooks/usePanels";
import DashboardRow from "./DashboardRow.vue";
import DashboardRenderAnimation from "./DashboardRenderAnimation.vue";

const monitorEditStore = useMonitorEditStore();
const {
  previewDashboardConfig,
  previewBoardOpened,
  debugOpened,
  debugCubeQuery,
} = storeToRefs(monitorEditStore);

const dateRange = ref<string>(shortcuts[0].value);
const timeRange = computed(() => {
  if (typeof dateRange.value === "string") {
    return dateRange.value;
  }
  return {
    start: (dateRange.value as [Date, Date])[0],
    end: (dateRange.value as [Date, Date])[1],
  };
});

const panels = usePanels(previewDashboardConfig, timeRange);
</script>

<template>
  <div class="p-3 w-full h-full overflow-y-auto">
    <ClientOnly>
      <Teleport to="#page-header-right">
        <div class="flex justify-end gap-2">
          <Button
            v-if="debugCubeQuery"
            class="rounded-full"
            variant="ghost"
            @click="debugOpened = !debugOpened"
          >
            <BugIcon :size="18" />
          </Button>
          <div class="flex items-center gap-2 mr-4">
            <TimeSelect v-model:value="dateRange" />
            <RefreshChart />
          </div>
        </div>
      </Teleport>
    </ClientOnly>

    <div v-if="previewDashboardConfig && previewBoardOpened">
      <DashboardRow
        v-for="(row, index) in previewDashboardConfig?.rows"
        :key="row.title"
        :title="row.title"
        :color-index="index"
      >
        <GridLayout
          v-model:layout="row.layout"
          :col-num="12"
          :row-height="80"
          :is-draggable="false"
          :is-resizable="false"
          vertical-compact
          use-css-transforms
        >
          <template #item="{ item }">
            <DashboardPanel
              v-if="panels[item.i]"
              :config="panels[item.i]"
              :debug="debugOpened"
            />
            <PlaceholderPanel v-else />
          </template>
        </GridLayout>
      </DashboardRow>
    </div>

    <!-- <div
      v-if="!previewDashboardConfig && previewBoardOpened"
      class="w-96 h-96 bg-red-400"
    >
      loading........
    </div> -->
    <DashboardRenderAnimation
      v-show="!previewDashboardConfig && previewBoardOpened"
    />
  </div>
</template>
