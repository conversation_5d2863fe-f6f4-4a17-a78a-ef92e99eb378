<script setup lang="ts">
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
} from "chart.js";
import type { Query } from "@cubejs-client/core";
import { useQueryData } from "../hooks/chart/useQueryData";
import { useSparklineOption } from "../hooks/chart/useSparklineOption";

// Define component name to fix linter warning
defineOptions({
  name: "SparklineChart",
});

// Register Chart.js components needed for line charts
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement);

interface SparklineProps {
  query: Query | null;
  height?: string;
  width?: string;
}

const props = withDefaults(defineProps<SparklineProps>(), {
  height: "90px",
  width: "100%",
});

const queryRef = computed(() => props.query);
const { resultSet } = useQueryData(queryRef);
const option = useSparklineOption(resultSet);
</script>

<template>
  <div
    class="sparkline-container"
    :style="{ height: props.height, width: props.width }"
  >
    <Line
      v-if="option.chartData.datasets.length > 0"
      :data="option.chartData"
      :options="option.chartOption"
      :update-mode="'none'"
    />
    <div
      v-else
      class="flex items-center justify-center h-full text-gray-400 text-sm"
    >
      No data
    </div>
    <div class="flex justify-between w-full mt-5">
      <span class="text-xs font-semibold text-muted-foreground">
        {{ option.measureLabel }}</span
      >
      <span class="text-xs font-semibold text-muted-foreground"
        >Last 15min</span
      >
    </div>
  </div>
</template>

<style scoped></style>
