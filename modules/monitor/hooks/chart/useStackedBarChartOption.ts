import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions } from "chart.js";
import { BORDER_COLOR_PALATTE, COLOR_PALATTE } from "~/const";
import { getValueFormatter, type UnitNames } from "~/utils/unit";
import { formatTimeLabel } from "./helpers";

export function useStackedBarChartOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: {} as ChartOptions<"bar">,
      };
    }

    try {
      const chartPivot = resultSet.value.chartPivot();
      const query = resultSet.value.query();

      if (!chartPivot || chartPivot.length === 0) {
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"bar">,
          chartOption: {} as ChartOptions<"bar">,
        };
      }

      const chartOptions: ChartOptions<"bar"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "white",
            bodyColor: "white",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
            mode: "index",
            intersect: false,
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            stacked: true,
            grid: {
              color: "rgba(0, 0, 0, 0.1)",
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
            },
          },
          x: {
            stacked: true,
            grid: {
              display: false,
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
            },
          },
        },
        // datasets: {
        //   bar: {
        //     maxBarThickness: undefined,
        //     categoryPercentage: 0.8,
        //     barPercentage: 0.9,
        //   },
        // },
      };

      if (query.measures?.length === 0) {
        console.warn("No measures found in query");
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"bar">,
          chartOption: chartOptions,
        };
      }

      // Handle stacked bar chart for time dimension + one dimension + one measure
      if (
        query.timeDimensions &&
        query.timeDimensions.length > 0 &&
        query.dimensions?.length === 1 &&
        query.measures?.length === 1
      ) {
        const timeDimension = query.timeDimensions[0];
        const measure = query.measures![0];

        const timeLabelsSet = new Set<string>();
        const dimensionValuesSet = new Set<string>();

        chartPivot.forEach((item) => {
          const timeValue = item.x;

          if (timeValue) {
            const formattedTime = formatTimeLabel(
              String(timeValue),
              timeDimension.granularity
            );
            timeLabelsSet.add(formattedTime);
          }

          // Extract dimension values from keys like "ens256,fct_flows_1m.connect_cnt"
          Object.keys(item).forEach((key) => {
            if (
              key !== "x" &&
              key !== "xValues" &&
              key.includes(`,${measure}`)
            ) {
              const dimensionValue = key.split(",")[0];
              dimensionValuesSet.add(dimensionValue);
            }
          });
        });

        const timeLabels = Array.from(timeLabelsSet).sort();
        const dimensionValues = Array.from(dimensionValuesSet);

        const datasets = dimensionValues.map((dimValue, index) => {
          const data: number[] = [];

          timeLabels.forEach((timeLabel) => {
            const dataPoint = chartPivot.find(
              (item: Record<string, unknown>) => {
                const formattedTime = formatTimeLabel(
                  String(item.x),
                  timeDimension.granularity
                );
                return formattedTime === timeLabel;
              }
            );

            const measureKey = `${dimValue},${measure}`;
            const value = dataPoint
              ? (dataPoint[measureKey] as number) || 0
              : 0;
            data.push(value);
          });

          return {
            label: String(dimValue),
            data,
            backgroundColor: getStackedBarColor(index, dimensionValues.length),
            borderColor: getStackedBarBorderColor(
              index,
              dimensionValues.length
            ),
            borderWidth: 1,
          };
        });

        const chartData: ChartData<"bar"> = {
          labels: timeLabels,
          datasets,
        };

        // Add y-axis label formatting
        if (chartOptions.scales?.y?.ticks) {
          try {
            const measures = resultSet.value.annotation().measures;
            const measureUnit = measures[measure].meta.unit;

            chartOptions.scales!.y.ticks.callback = (
              tickValue: string | number
            ) => {
              const formatter = getValueFormatter(measureUnit as UnitNames);
              const result = formatter(Number(tickValue));
              return `${result.value} ${result.unit}`;
            };
          } catch (error) {
            console.warn("Failed to set y-axis formatter:", error);
          }
        }

        return {
          chartData,
          chartOption: chartOptions,
        };
      }

      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing stacked bar chart data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: {} as ChartOptions<"bar">,
      };
    }
  });
}

function getStackedBarColor(index: number, _total: number): string {
  return COLOR_PALATTE[index % COLOR_PALATTE.length];
}

function getStackedBarBorderColor(index: number, _total: number): string {
  return BORDER_COLOR_PALATTE[index % BORDER_COLOR_PALATTE.length];
}
