import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions } from "chart.js";
import { formatTimeLabel, getMeasureLabel } from "./helpers";

export function useSparklineOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }

    try {
      const chartPivot = resultSet.value.chartPivot();
      const query = resultSet.value.query();

      if (!chartPivot || chartPivot.length === 0) {
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"line">,
          chartOption: {} as ChartOptions<"line">,
        };
      }

      // Sparkline-specific options - minimal UI, no axes, no legend
      const chartOptions: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        animation: {
          duration: 0, // Disable animations for performance
        },
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "white",
            bodyColor: "white",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
          },
        },
        scales: {
          x: {
            display: false, // Hide x-axis
          },
          y: {
            display: false, // Hide y-axis
          },
        },
        elements: {
          point: {
            radius: 0, // Hide data points normally
            hoverRadius: 4, // Show small point on hover for tooltip

            hoverBorderColor: "white",
            hoverBorderWidth: 2,
          },
          line: {
            borderWidth: 2,
            tension: 0.4, // Increased tension for much smoother curves
          },
        },
      };

      if (query.measures?.length === 0) {
        console.warn("No measures found in query");
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"line">,
          chartOption: chartOptions,
        };
      }

      if (
        query.timeDimensions &&
        query.timeDimensions.length > 0 &&
        query.dimensions?.length === 0 &&
        query.measures?.length === 1
      ) {
        const timeDimension = query.timeDimensions[0];
        const measure = query.measures![0];

        const labels: string[] = [];
        const data: number[] = [];

        chartPivot.forEach((item: Record<string, unknown>) => {
          const timeValue = item.x;
          const measureValue = (item[measure] as number) || 0;

          if (timeValue) {
            const formattedTime = formatTimeLabel(
              String(timeValue),
              timeDimension.granularity
            );
            labels.push(formattedTime);
            data.push(measureValue);
          }
        });

        // Determine line color based on trend (optional enhancement)
        const firstValue = data[0] || 0;
        const lastValue = data[data.length - 1] || 0;
        const isPositiveTrend = lastValue >= firstValue;
        const lineColor = isPositiveTrend
          ? "rgba(34, 197, 94, 1)"
          : "rgba(239, 68, 68, 1)"; // green-500 : red-500

        const chartData: ChartData<"line"> = {
          labels,
          datasets: [
            {
              label: "", // No label needed for sparklines
              data,
              borderColor: lineColor,

              tension: 0.4, // Dataset-level tension for smoother curves
              borderWidth: 2,
              pointRadius: 0, // Ensure no points are shown normally
              pointHoverRadius: 4, // Show point on hover for tooltip
              pointHoverBackgroundColor: lineColor,
              pointHoverBorderColor: "white",
              pointHoverBorderWidth: 2,
              cubicInterpolationMode: "monotone" as const, // Use monotone cubic interpolation for natural curves
            },
          ],
        };

        return {
          measureLabel: getMeasureLabel(resultSet.value, measure),
          chartData,
          chartOption: chartOptions,
        };
      }

      return {
        measure: "",
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing sparkline data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }
  });
}
