import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions } from "chart.js";
import { formatTimeLabel, getMeasureLabel } from "./helpers";

export function useBarChartOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: {} as ChartOptions<"bar">,
      };
    }

    try {
      const chartPivot = resultSet.value.chartPivot();
      const query = resultSet.value.query();

      if (!chartPivot || chartPivot.length === 0) {
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"bar">,
          chartOption: {} as ChartOptions<"bar">,
        };
      }

      const chartOptions: ChartOptions<"bar"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "white",
            bodyColor: "white",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: "rgba(0, 0, 0, 0.1)",
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
            },
          },
          x: {
            grid: {
              display: false,
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
            },
          },
        },
        datasets: {
          bar: {
            maxBarThickness: undefined,
            categoryPercentage: 0.8,
            barPercentage: 0.9,
          },
        },
      };

      if (query.measures?.length === 0) {
        console.warn("No measures found in query");
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"bar">,
          chartOption: chartOptions,
        };
      }

      if (
        query.timeDimensions &&
        query.timeDimensions.length > 0 &&
        query.dimensions?.length === 0 &&
        query.measures?.length === 1
      ) {
        const timeDimension = query.timeDimensions[0];
        const measure = query.measures![0];

        const labels: string[] = [];
        const data: number[] = [];

        chartPivot.forEach((item: Record<string, unknown>) => {
          const timeValue = item.x;
          const measureValue = (item[measure] as number) || 0;

          if (timeValue) {
            const formattedTime = formatTimeLabel(
              String(timeValue),
              timeDimension.granularity
            );
            labels.push(formattedTime);
            data.push(measureValue);
          }
        });

        const chartData: ChartData<"bar"> = {
          labels,
          datasets: [
            {
              label: getMeasureLabel(resultSet.value, measure),
              data,
              backgroundColor: "rgba(59, 130, 246, 0.8)", // blue-500
              borderColor: "rgba(59, 130, 246, 1)",
              borderWidth: 1,
            },
          ],
        };

        if (chartOptions.scales?.y?.ticks) {
          const measures = resultSet.value.annotation().measures;
          const measureUnit = measures[measure].meta.unit;

          chartOptions.scales!.y.ticks.callback = (
            tickValue: string | number
          ) => {
            const formatter = getValueFormatter(measureUnit as UnitNames);
            const result = formatter(Number(tickValue));
            return `${result.value} ${result.unit}`;
          };
        }

        return {
          chartData,
          chartOption: chartOptions,
        };
      }

      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing bar chart data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: {} as ChartOptions<"bar">,
      };
    }
  });
}
