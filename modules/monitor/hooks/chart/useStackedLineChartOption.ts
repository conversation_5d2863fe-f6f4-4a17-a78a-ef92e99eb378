import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions } from "chart.js";
import type { BarLineData } from "~/modules/monitor/types";
import {
  extractMetaDataTitles,
  getChartData,
} from "~/modules/monitor/helpers/chartTypeHelper";
import { getValueFormatter, type UnitNames } from "~/utils/unit";
import { formatTimeLabel } from "~/modules/monitor/helpers/chartData";

import { COLOR_PALATTE, BORDER_COLOR_PALATTE } from "~/const";
const isValidCategory = (category: string): boolean => {
  if (!category || category.trim() === "") {
    return false;
  }
  const ipv6Regex =
    /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
  if (ipv6Regex.test(category)) {
    return false;
  }
  const invalidValues = ["null", "undefined", "NaN", "invalid", "unknown"];
  if (invalidValues.includes(category.toLowerCase())) {
    return false;
  }

  return true;
};

function convertToChartData(transformedData: BarLineData[]): ChartData<"line"> {
  // 过滤掉无效的 category
  const filteredData = transformedData.filter((item) =>
    isValidCategory(item.category)
  );

  // 对 label 进行时间格式化处理
  const uniqueLabels = [
    ...new Set(filteredData.map((item) => formatTimeLabel(String(item.label)))),
  ].sort();

  const uniqueCategories = [
    ...new Set(filteredData.map((item) => item.category)),
  ];

  const datasets = uniqueCategories.map((category, index) => {
    const data = uniqueLabels.map((label) => {
      const item = filteredData.find(
        (d) =>
          formatTimeLabel(String(d.label)) === label && d.category === category
      );
      return item ? Number(item.value) : 0;
    });

    const colorIndex = index % COLOR_PALATTE.length;
    return {
      label: category,
      data: data,
      backgroundColor: COLOR_PALATTE[colorIndex],
      borderColor: BORDER_COLOR_PALATTE[colorIndex],
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 0,
      stack: "stack1",
    };
  });

  return {
    labels: uniqueLabels,
    datasets: datasets,
  };
}

export function useStackedLineChartOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }

    try {
      const metaData = resultSet.value?.annotation();

      let unit: UnitNames | undefined;
      let valueFormatter: (value: number) => {
        value: number | string;
        unit: string;
        fullUnit: UnitNames | string;
      };
      if (metaData?.measures) {
        const measureUnits = Object.values(metaData.measures).map(
          (m) => m?.meta?.unit as UnitNames
        );
        unit = measureUnits[0];
        valueFormatter = getValueFormatter(unit);
      } else {
        valueFormatter = getValueFormatter();
      }

      // Chart options
      const chartOptions: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        plugins: {
          legend: {
            display: true,
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "white",
            bodyColor: "white",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
            mode: "index",
            intersect: false,
            callbacks: {
              label: function (context) {
                const value = context.parsed.y;
                const formatted = valueFormatter(value);
                const label = context.dataset.label || "";
                return `${label}: ${formatted.value}${formatted.unit}`;
              },
            },
          },
        },
        scales: {
          x: {
            stacked: true,
            grid: {
              display: false,
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
              maxTicksLimit: 4,
            },
          },
          y: {
            beginAtZero: true,
            stacked: true,
            grid: {
              color: "rgba(0, 0, 0, 0.1)",
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
              callback: function (value) {
                if (typeof value === "number") {
                  const formatted = valueFormatter(value);
                  return `${formatted.value}${formatted.unit}`;
                }
                return value;
              },
            },
          },
        },
        interaction: {
          mode: "index",
          intersect: false,
        },
      };

      if (metaData) {
        const allTitles = extractMetaDataTitles(metaData);
        const measuresStr = Object.keys(metaData?.measures ?? {}).join(",");
        const transformedData = getChartData(
          resultSet.value?.chartPivot() || [],
          allTitles,
          measuresStr
        );

        if (transformedData && transformedData.length > 0) {
          const chartData = convertToChartData(
            transformedData as BarLineData[]
          );
          return {
            chartData,
            chartOption: chartOptions,
          };
        }
      }

      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing stacked line chart data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }
  });
}
