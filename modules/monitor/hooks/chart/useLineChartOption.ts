import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions, TooltipItem } from "chart.js";
import { BORDER_COLOR_PALATTE, COLOR_PALATTE } from "~/const";
import { getValueFormatter } from "~/utils/unit";
import { formatTimeLabel, getMeasureLabel } from "./helpers";

// 默认数据集样式
const DEFAULT_DATASET_STYLE = {
  borderWidth: 2,
  fill: false,
  tension: 0.4,
  pointRadius: 0,
  pointHoverRadius: 0,
  pointBorderWidth: 0,
  pointBackgroundColor: "transparent",
};

// 创建空的图表数据
const createEmptyChartData = (): {
  chartData: ChartData<"line">;
  chartOption: ChartOptions<"line">;
} => ({
  chartData: { labels: [], datasets: [] },
  chartOption: {},
});

export function useLineChartOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) return createEmptyChartData();

    try {
      const chartPivot = resultSet.value.chartPivot();
      const query = resultSet.value.query();
      const metaData = resultSet.value.annotation();

      if (!chartPivot?.length || !query.measures?.length) {
        return createEmptyChartData();
      }

      // 创建图表配置
      const chartOptions: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            mode: "index",
            intersect: false,
            backgroundColor: "#fff",
            titleColor: "black",
            bodyColor: "black",
            borderColor: "rgba(156, 163, 175, 0.5)",
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              labelTextColor: (ctx: TooltipItem<"line">) =>
                Array.isArray(ctx.dataset.borderColor)
                  ? ctx.dataset.borderColor[0]
                  : ctx.dataset.borderColor,
              label: (ctx: TooltipItem<"line">) => {
                const value =
                  typeof ctx.parsed.y === "number" ? ctx.parsed.y : 0;
                const label = ctx.dataset.label || "";
                return `${label}: ${value}`;
              },
            },
          },
        },
        scales: {
          x: {
            display: true,
            grid: { color: "rgba(156, 163, 175, 0.2)", lineWidth: 1 },
            ticks: { color: "black", maxTicksLimit: 4 },
          },
          y: {
            display: true,
            beginAtZero: false,
            grid: { color: "rgba(156, 163, 175, 0.2)", lineWidth: 1 },
            ticks: { color: "black", maxTicksLimit: 6 },
          },
        },
        interaction: { mode: "nearest", axis: "x", intersect: false },
        elements: {
          point: {
            hoverBackgroundColor: "#fff",
            hoverBorderWidth: 2,
            radius: 0,
            hoverRadius: 0,
          },
          line: { tension: 0.4, borderWidth: 2 },
        },
      };

      const timeDimension = query.timeDimensions?.[0];

      // 获取时间标签
      const getTimeLabels = (): string[] => {
        const labels = chartPivot
          .map((item: Record<string, unknown>) => {
            const timeValue = item.x;
            return timeValue
              ? formatTimeLabel(String(timeValue), timeDimension?.granularity)
              : "";
          })
          .filter(Boolean);
        return [...new Set(labels)].sort();
      };

      // 创建数据集
      const createDataset = (
        label: string,
        data: number[],
        colorIndex: number,
        unit?: string
      ) => ({
        label: unit ? `${label} (${unit})` : label,
        data,
        backgroundColor: COLOR_PALATTE[colorIndex % COLOR_PALATTE.length],
        borderColor:
          BORDER_COLOR_PALATTE[colorIndex % BORDER_COLOR_PALATTE.length],
        ...DEFAULT_DATASET_STYLE,
      });

      // 查找数据点并处理单位
      const findDataPoint = (
        labels: string[],
        measure: string,
        unit: string,
        dimensionKey?: string,
        dimensionValue?: string
      ): { data: number[]; unit: string } => {
        const formatter = getValueFormatter(unit as UnitNames);
        let dataUnit = "";

        const data = labels.map((label) => {
          const item = chartPivot.find((pivot: Record<string, unknown>) => {
            const timeValue = pivot.x;
            const formattedTime = timeValue
              ? formatTimeLabel(String(timeValue), timeDimension?.granularity)
              : "";
            const matchesTime = formattedTime === label;
            const matchesDimension =
              !dimensionKey || pivot[dimensionKey] === dimensionValue;
            return matchesTime && matchesDimension;
          });

          const value = item ? (item[measure] as number) || 0 : 0;
          const formatted = formatter(value);
          dataUnit = formatted.unit;
          return Number(formatted.value);
        });

        return { data, unit: dataUnit };
      };

      const labels = getTimeLabels();
      const datasets: ChartData<"line">["datasets"] = [];

      if (query.dimensions?.length && query.measures.length === 1) {
        const measure = query.measures[0];
        const dimension = query.dimensions[0];
        const measureMeta = metaData?.measures?.[measure];
        const unit = measureMeta?.meta?.unit || "unknown";

        const dimensionValues = [
          ...new Set(chartPivot.map((item) => item[dimension] as string)),
        ];

        dimensionValues.forEach((dimValue, index) => {
          const result = findDataPoint(
            labels,
            measure,
            unit,
            dimension,
            dimValue
          );
          datasets.push(
            createDataset(String(dimValue), result.data, index, result.unit)
          );
        });
      } else {
        // 多指标情况
        query.measures.forEach((measure, index) => {
          const measureMeta = metaData?.measures?.[measure];
          const unit = measureMeta?.meta?.unit || "unknown";
          const result = findDataPoint(labels, measure, unit);
          const label = getMeasureLabel(resultSet.value!, measure);
          datasets.push(createDataset(label, result.data, index, result.unit));
        });
      }

      return {
        chartData: { labels, datasets },
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing line chart data:", error);
      return createEmptyChartData();
    }
  });
}
