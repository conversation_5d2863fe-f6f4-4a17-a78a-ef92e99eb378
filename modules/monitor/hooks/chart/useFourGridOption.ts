import type { ResultSet } from "@cubejs-client/core";
import type { ShallowRef } from "vue";

const emptyValues = () =>
  new Array(4).fill(0).map(() => ({ value: 0, unit: "", title: "" }));

export function useFourGridOption(resultSet: ShallowRef<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return emptyValues();
    }

    try {
      const pivotData = resultSet.value.chartPivot();
      if (!pivotData || pivotData.length === 0) {
        return emptyValues();
      }

      const query = resultSet.value.query();
      const measures = query.measures;

      const values: { value: number; unit: string; title: string }[] = [];
      for (const measure of measures ?? []) {
        const value = pivotData[0]?.[measure] || 0;
        const unit =
          resultSet.value.annotation().measures[measure]?.meta?.unit ?? "";

        const title =
          resultSet.value.annotation().measures[measure]?.shortTitle ?? "";
        const format = getValueFormatter(unit);
        values.push({
          ...(format(value) as { value: number; unit: "" }),
          title,
        });
      }

      return values;
    } catch (error) {
      console.error("Error processing chart data:", error);
      return emptyValues();
    }
  });
}
