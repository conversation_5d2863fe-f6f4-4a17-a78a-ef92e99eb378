import type { ResultSet } from "@cubejs-client/core";

export function formatTimeLabel(
  timeValue: string,
  granularity?: string
): string {
  const date = new Date(timeValue);

  switch (granularity) {
    case "hour":
      return date.toLocaleString("en-US", {
        month: "2-digit",
        day: "numeric",
        hour: "numeric",
        hour12: false,
      });
    case "day":
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    case "week":
      return `Week ${date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })}`;
    case "month":
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
      });
    case "year":
      return date.getFullYear().toString();
    default:
      return date.toLocaleDateString();
  }
}

export function getMeasureLabel(resultSet: ResultSet, measure: string): string {
  try {
    const annotation = resultSet.annotation();
    return annotation.measures[measure]?.shortTitle || measure;
  } catch {
    return measure;
  }
}
