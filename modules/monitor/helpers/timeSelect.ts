import dayjs from "dayjs";

export function intervalToMs(interval: string): number {
  if (!interval || interval === "" || interval === "off") return 0;
  if (interval === "auto") return 5000;
  const unit = interval.slice(-1);
  const value = parseInt(interval.slice(0, -1));
  switch (unit) {
    case "s":
      return value * 1000;
    case "m":
      return value * 60 * 1000;
    case "h":
      return value * 60 * 60 * 1000;
    case "d":
      return value * 24 * 60 * 60 * 1000;
    default: {
      const numValue = parseInt(interval);
      return isNaN(numValue) ? 0 : numValue * 1000;
    }
  }
}

export function getTimeRange(
  unit: "minute" | "hour" | "day",
  amount: number
): [Date, Date] {
  const end = dayjs().toDate();
  const start = dayjs().subtract(amount, unit).toDate();
  return [start, end];
}

export const shortcuts = [
  {
    text: "Last 5 minutes",
    value: "last 5 minutes",
  },
  {
    text: "Last 15 minutes",
    value: "last 15 minutes",
  },
  {
    text: "Last 30 minutes",
    value: "last 30 minutes",
  },
  {
    text: "Last 1 hour",
    value: "last 1 hour",
  },
  {
    text: "Last 3 hours",
    value: "last 3 hours",
  },
  {
    text: "Last 6 hours",
    value: "last 6 hours",
  },

  {
    text: "Last 12 hours",
    value: "last 12 hours",
  },
  {
    text: "Last 24 hours",
    value: "last 24 hours",
  },
  {
    text: "Last 2 days",
    value: "last 2 days",
  },
  {
    text: "Last 7 days",
    value: "last 7 days",
  },
  {
    text: "Last 30 days",
    value: "last 30 days",
  },
];
