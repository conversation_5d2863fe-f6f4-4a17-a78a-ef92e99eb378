import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import type { BarLineData } from "../types";
import type { ChartData, ChartDataset } from "chart.js";

dayjs.extend(utc);
dayjs.extend(timezone);

export interface ChartDataOptions {
  colors: Array<{ border: string; background: string } | string>;
  stacked?: boolean;
  fill?: boolean;
}
const Colors = [
  "#3C9AE8",
  "#FF8326",
  "#17BBDC",
  "#E145CB",
  "#36B248",
  "#FF4C57",
  "#EBC923",
  "#8F52CC",
];

export function formatTimeLabel(timeString: string): string {
  const localTimezone = dayjs.tz.guess();
  const d = dayjs.utc(timeString).tz(localTimezone);

  if (!d.isValid()) return timeString;
  if (d.second() !== 0) return d.format("YYYY-MM-DD HH:mm:ss");
  if (d.minute() !== 0) return d.format("YYYY-MM-DD HH:mm");
  if (d.hour() !== 0) return d.format("YYYY-MM-DD HH:00");
  if (d.date() !== 0) return d.format("YYYY-MM-DD");
  if (d.month() !== 0) return d.format("YYYY-MM");
  return d.format("YYYY");
}

export function getLineChartData(data: BarLineData[]) {
  const uniqueTimeValues = [...new Set(data.map((item) => item.label))].sort();
  const uniqueCategories = [...new Set(data.map((item) => item.category))];
  // 为每个 category 创建一个数据集
  const datasets = uniqueCategories.map((category, index) => {
    const values = uniqueTimeValues.map((x) => {
      const item = data.find((d) => d.label === x && d.category === category);
      return item ? item.value : 0;
    });
    const color = Colors[index % Colors.length];
    // 统一用字符串色值
    return {
      label: category,
      data: values,
      backgroundColor: color,
      borderColor: color,
      borderWidth: 2,
      pointRadius: 0,
      pointHoverRadius: 0,
      tension: 0.4,
    };
  });

  return {
    labels: uniqueTimeValues.map((x) => {
      // 格式化时间显示
      if (typeof x === "string" && x.includes("T")) {
        return formatTimeLabel(x);
      }
      return x;
    }),
    datasets,
  };
}

export function getBarChartData(
  data: BarLineData[]
): ChartData<"bar", number[], unknown> {
  // 获取所有唯一的 label（作为 y 轴）
  const uniqueLabels = [...new Set(data.map((item) => item.label))];
  // 获取所有唯一的 category（作为分组/颜色）
  const uniqueCategories = [...new Set(data.map((item) => item.category))];

  // 构造每个 category 的 dataset
  const datasets: ChartDataset<"bar", number[]>[] = uniqueCategories.map(
    (category, idx) => {
      const color = Colors[idx % Colors.length];
      const values = uniqueLabels.map((label) => {
        const item = data.find(
          (d) => d.label === label && d.category === category
        );
        return item ? Number(item.value) : 0;
      });
      return {
        label: String(category),
        data: values,
        backgroundColor: color,
        borderColor: color,
        borderWidth: 1,
      };
    }
  );

  return {
    labels: uniqueLabels.map(String),
    datasets,
  };
}
